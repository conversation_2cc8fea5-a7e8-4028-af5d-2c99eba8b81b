# GuardrailAgent - AI-Powered Audit Agent

## Overview

The GuardrailAgent is a reasoning-based audit agent in an AI-powered agentic validation pipeline. It audits outputs from autonomous validation agents (FileValidationAgent, DataValidationAgent, SP_Parser_Agent) **without redoing their work**, using structured prompts and configuration-driven logic.

## Key Features

- **AI-Powered Reasoning**: Uses OpenAI GPT-4 for intelligent audit analysis
- **Configuration-Driven**: All validation logic loaded from YAML configurations
- **Structured Prompts**: Uses templates from `ContextGuardrail/` for consistent analysis
- **Dual Logging**: Both structured JSON logs and human-readable logs
- **No Hardcoding**: Dynamic thresholds, schema validation, and escalation logic

## Architecture

```
SP_Parser_Agent ───▶ rules_template.json + column_template.json
                                │
                                ▼
          FileValidationAgent ─▶ file_validation_output.json
                                │
                                ▼
     🛡️ GuardrailAgent (Audit Phase 1: FileValidationAgent)
                                │
                            [PASS] ─────────────┐
                                │               │
                            [FAIL]              ▼
                            └────── escalate/log │
                                               ▼
          DataValidationAgent ─▶ data_validation_output.json
                                │
                                ▼
     🛡️ GuardrailAgent (Audit Phase 2: DataValidationAgent)
                                │
                            [PASS] ──────▶ Final report
                            [FAIL] ──────▶ Escalation + stop
```

## Input Format

```python
guardrail.audit_agent_output(
    agent_name="FileValidationAgent",           # Name of agent being audited
    agent_phase="Audit Phase 1",               # "Audit Phase 1" or "Audit Phase 2"
    agent_output=agent_output_dict              # JSON output from upstream agent
)
```

## Output Format

```json
{
  "agent": "FileValidationAgent",
  "phase": "Audit Phase 1",
  "status": "PASSED" | "WARNING" | "CRITICAL",
  "findings": [
    "Short explanation: e.g., 'Column NAV missing from output'",
    "Violation: Invalid flag detected on row 4 – exceeds threshold",
    "Hallucination: Detected reasoning inconsistent with input schema"
  ],
  "escalation": "RECOMMENDED" | "NOT_NEEDED" | null
}
```

## Configuration Files

### `config/agent_profiles.yaml`
- Complete expected schemas for each agent
- Agent capabilities and limitations
- Performance thresholds

### `config/thresholds.yaml`
- Dynamic threshold configurations
- Agent-specific performance limits
- Escalation trigger points

### `ContextGuardrail/` Directory
- `base_instruction_prompt.txt`: Core agent instructions
- `schema_violation_prompt.txt`: Schema compliance checking
- `hallucination_check_prompt.txt`: Hallucination detection
- `escalation_policy_prompt.txt`: Escalation decision framework

## Audit Process

1. **Schema Compliance Check**: Verify output matches expected structure
2. **Hallucination Detection**: Check for unsupported logic or fabricated content
3. **Threshold Violation Assessment**: Compare against dynamic thresholds
4. **Escalation Assessment**: Determine if human intervention needed

## Usage

### Production Usage
```bash
python run_guardrail_production.py FileValidationAgent "Audit Phase 1" test_data/file_validation_output.json
```

### Testing
```bash
python test_guardrail_agent.py
```

### Programmatic Usage
```python
from agents.guardrail_agent import GuardrailAgent

guardrail = GuardrailAgent()
result = guardrail.audit_agent_output(
    agent_name="FileValidationAgent",
    agent_phase="Audit Phase 1", 
    agent_output=agent_output_data
)
```

## Setup

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set Environment Variables**:
   ```bash
   cp .env.template .env
   # Edit .env and add your OPENAI_API_KEY
   ```

3. **Run Tests**:
   ```bash
   python test_guardrail_agent.py
   ```

## Logging

The GuardrailAgent implements dual logging:

- **Structured JSON Logs**: `data/audit_logs/audit_logs.jsonl`
- **Human-Readable Logs**: `data/audit_logs/audit_human_readable.log`
- **Audit Reports**: `data/reports/audit_report_*.json`

## Agent Chain Context

The GuardrailAgent is strictly part of a sequential audit chain and does NOT:
- Recalculate or re-validate file or business logic directly
- Hardcode key names, thresholds, column rules, or agent behaviors
- Block or mask sensitive data (all data is visible during auditing)

It ONLY audits and explains the upstream agent's output based on configuration-driven logic.
