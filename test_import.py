#!/usr/bin/env python3

import sys
import os

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from agents.guardrail_agent import GuardrailAgent
    print("✅ Successfully imported GuardrailAgent")

    # Test basic initialization - will automatically load from .env
    try:
        agent = GuardrailAgent()
        print("✅ Successfully initialized GuardrailAgent with .env API key")
    except ValueError as e:
        print(f"❌ Error initializing GuardrailAgent: {e}")
        print("⚠️  Make sure OPENAI_API_KEY is set in the .env file")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
except Exception as e:
    print(f"❌ Unexpected error: {e}")

print("\nProject structure:")
print("- agents/")
print("  - guardrail_agent.py")
print("  - process_compliance.py") 
print("  - reasoning_auditor.py")
print("  - security_monitor.py")
print("- demo/")
print("  - demo_runner.py")
print("  - demo_scenarios.yaml")
print("- test_data/")
print("- config/")
