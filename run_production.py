#!/usr/bin/env python3

import sys
import os
import json
import argparse
import glob
from datetime import datetime
from dotenv import load_dotenv

# Add current path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load env
load_dotenv()

# === Use your telemetry logger ===
from core.telemetry_logger import TelemetryLogger
logger = TelemetryLogger()  # logs both console and file

def get_files_to_audit(args):
    if args.file and os.path.exists(args.file):
        return [args.file]
    elif args.filename:
        path = os.path.join('test_data', args.filename)
        if os.path.exists(path):
            return [path]
        else:
            logger.log_error(f"File not found in test_data/: {args.filename}")
            sys.exit(1)
    else:
        files = glob.glob('test_data/*.json')
        if not files:
            logger.log_error("No JSON files found in test_data/")
            sys.exit(1)
        return sorted(files)

def load_agent_output(filepath):
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            output = json.load(f)
        if 'metadata' not in output:
            logger.log_warning(f"{filepath} missing metadata")
        return output
    except Exception as e:
        logger.log_error(f"Failed to load {filepath}: {e}")
        return None

def initialize_guardrail_agent():
    try:
        from agents.guardrail_agent import GuardrailAgent
        logger.log_step("Initializing Guardrail Agent...")
        return GuardrailAgent()
    except Exception as e:
        logger.log_error(f"Guardrail Agent initialization failed: {e}")
        sys.exit(1)

def audit_single_file(agent, filepath, show_details=True):
    logger.log_step(f"Auditing: {filepath}")
    output = load_agent_output(filepath)
    if output is None:
        return None

    try:
        metadata = output.get('metadata', {})
        agent_type = metadata.get('agent_type', 'UNKNOWN')
        task_id = metadata.get('task_id', 'UNKNOWN')

        logger.log_step(f"Agent Type: {agent_type}, Task ID: {task_id}")

        start = datetime.utcnow()
        result = agent.audit_agent_output(output)
        duration = (datetime.utcnow() - start).total_seconds() * 1000

        logger.log_done(f"Status: {result.get('status', 'UNKNOWN')} in {duration:.2f}ms")

        if show_details and result['status'] != 'PASSED':
            for cat, issues in result['findings'].items():
                if issues:
                    logger.log_warning(f"{cat.title()}:")
                    for issue in issues:
                        logger.log_warning(f" - {issue}")

        if 'escalation' in result:
            reason = result['escalation'].get("reason", "Unspecified")
            logger.log_escalation(reason)

        return result
    except Exception as e:
        logger.log_error(f"Audit failed: {e}")
        return None

def main():
    parser = argparse.ArgumentParser(description="Run Guardrail Agent in production")
    parser.add_argument('filename', nargs='?', help='Filename from test_data/')
    parser.add_argument('--file', help='Full file path')
    parser.add_argument('--summary', action='store_true')
    args = parser.parse_args()

    files = get_files_to_audit(args)
    agent = initialize_guardrail_agent()

    results = []
    for path in files:
        result = audit_single_file(agent, path, show_details=not args.summary)
        if result:
            results.append({
                'file': path,
                'status': result.get('status', 'ERROR'),
                'agent_type': result.get('agent_type', 'UNKNOWN'),
                'escalation': 'escalation' in result
            })

    if len(files) > 1 or args.summary:
        logger.log_step("Generating summary...")
        total = len(results)
        by_status = {}
        escalations = 0

        for r in results:
            by_status[r['status']] = by_status.get(r['status'], 0) + 1
            if r['escalation']:
                escalations += 1

        logger.log_ok(f"Files Audited: {total}")
        for status, count in by_status.items():
            logger.log_ok(f"{status}: {count}")
        if escalations:
            logger.log_warning(f"Escalations: {escalations}")

        logger.log_ok("Log files:")
        logger.log_ok(" - audit_logs.jsonl")
        logger.log_ok(" - audit_human_readable.log")

if __name__ == "__main__":
    main()
