from models.agent_output import Metadata

class EscalationHandler:
    def __init__(self):
        self.templates = {
            "SECURITY": {
                "title": "CRITICAL: Potential Data Leak Detected",
                "description": "Sensitive information identified in agent output"
            },
            "REASONING": {
                "title": "CRITICAL: AI Reasoning Inconsistency Detected",
                "description": "Potential hallucinations or logical errors in agent conclusions"
            },
            "PROCESS": {
                "title": "WARNING: Agent Process Violation",
                "description": "Agent failed to follow defined validation process"
            }
        }

    def generate_escalation(self, findings, metadata: Metadata):
        """Generate escalation based on findings severity"""
        # Determine escalation type
        if findings.get("security_issues"):
            return self._format_escalation("SECURITY", findings, metadata)
        if findings.get("reasoning_issues"):
            return self._format_escalation("REASONING", findings, metadata)
        if findings.get("process_issues"):
            return self._format_escalation("PROCESS", findings, metadata)

        return {"status": "NO_ESCALATION_NEEDED"}
    
    def _format_escalation(self, issue_type, findings, metadata: Metadata):
        """Create structured escalation object"""
        template = self.templates[issue_type]
        details = []

        if issue_type == "SECURITY":
            details = findings["security_issues"]
        elif issue_type == "REASONING":
            details = findings["reasoning_issues"]
        elif issue_type == "PROCESS":
            details = findings["process_issues"]

        return {
            "type": issue_type,
            "severity": "CRITICAL" if issue_type in ["SECURITY", "REASONING"] else "WARNING",
            "title": template["title"],
            "description": template["description"],
            "agent_type": metadata.agent_type,
            "agent_version": metadata.agent_version,
            "task_id": metadata.task_id,
            "details": details,
            "action_required": "Immediate human review",
            "timestamp": metadata.timestamp
        }