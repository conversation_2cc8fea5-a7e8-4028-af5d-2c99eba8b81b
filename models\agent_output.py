"""
Pydantic models for agent output validation.

This module defines the structure and validation rules for agent outputs
processed by the Guardrail Agent system.
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


class Metadata(BaseModel):
    """Metadata structure for all agent outputs."""
    agent_type: str = Field(..., description="Type of agent that generated the output")
    agent_version: Optional[str] = Field(None, description="Version of the agent")
    task_id: Optional[str] = Field(None, description="Unique identifier for the task")
    processing_time_ms: Optional[int] = Field(None, description="Processing time in milliseconds")
    timestamp: Optional[str] = Field(None, description="ISO timestamp of when output was generated")

    class Config:
        """Pydantic configuration."""
        extra = "forbid"  # Prevent additional fields


class AgentOutput(BaseModel):
    """
    Standardized structure for all agent outputs in the Guardrail Agent system.
    
    This model enforces validation and provides type safety for agent outputs
    while maintaining flexibility for different agent types through optional fields.
    """
    
    # Required field
    metadata: Metadata = Field(..., description="Required metadata for all agent outputs")
    
    # Optional fields used by different agents
    file_name: Optional[str] = Field(None, description="Name of the file being processed")
    valid_rows: Optional[int] = Field(None, description="Number of valid rows processed")
    invalid_rows: Optional[int] = Field(None, description="Number of invalid rows found")
    error_messages: Optional[List[str]] = Field(None, description="List of error messages")
    validation_coverage: Optional[float] = Field(None, description="Coverage percentage for validation")
    
    override_rate: Optional[float] = Field(None, description="Rate of overrides applied")
    override_justifications: Optional[List[str]] = Field(None, description="Justifications for overrides")
    rule_violations: Optional[List[str]] = Field(None, description="List of rule violations found")
    validated_dataset: Optional[Dict[str, Any]] = Field(None, description="Information about validated dataset")
    
    rules_json: Optional[Dict[str, Any]] = Field(None, description="Parsed rules in JSON format")
    column_template: Optional[Dict[str, str]] = Field(None, description="Column template definitions")
    parsing_confidence: Optional[float] = Field(None, description="Confidence level of parsing results")

    class Config:
        """Pydantic configuration."""
        extra = "forbid"  # Prevent additional fields
        validate_assignment = True  # Validate on assignment
        
    def model_dump_dict(self) -> Dict[str, Any]:
        """
        Convert the model to a dictionary, excluding None values.
        
        Returns:
            Dict[str, Any]: Dictionary representation excluding None values
        """
        return self.model_dump(exclude_none=True)
    
    @classmethod
    def parse_agent_output(cls, raw_output: Dict[str, Any]) -> 'AgentOutput':
        """
        Parse raw agent output dictionary into validated AgentOutput model.
        
        Args:
            raw_output: Raw dictionary from agent
            
        Returns:
            AgentOutput: Validated agent output model
            
        Raises:
            ValidationError: If the input doesn't match the expected structure
        """
        return cls.model_validate(raw_output)
