import json
from core.prompt_templates import get_reasoning_prompt
from models.agent_output import AgentOutput

class ReasoningAuditor:
    def __init__(self, client):
        self.client = client

    def audit_reasoning(self, agent_output: AgentOutput, expected_structure):
        """Audit agent's reasoning without redoing its work"""
        try:
            agent_type = agent_output.metadata.agent_type
            # Convert Pydantic model to dict for JSON serialization
            agent_output_dict = agent_output.model_dump_dict()
            prompt = get_reasoning_prompt(agent_type).format(
                agent_output=json.dumps(agent_output_dict, indent=2),
                expected_structure=json.dumps(expected_structure, indent=2)
            )

            response = self.client.chat.completions.create(
                model="gpt-4o-mini",  # Using GPT-4 mini as requested
                messages=[
                    {"role": "system", "content": "You are an AI reasoning auditor"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            result = response.choices[0].message.content.strip()
            return self._parse_audit_result(result)

        except Exception as e:
            return [f"Reasoning audit failed: {str(e)}"]

    def _parse_audit_result(self, result):
        """Parse AI response into structured findings"""
        if "VALID" in result:
            return []
        
        # Extract issues from AI response
        issues = []
        if "INCONSISTENCIES:" in result:
            issues = result.split("INCONSISTENCIES:")[1].split("\n")
        elif "ISSUES:" in result:
            issues = result.split("ISSUES:")[1].split("\n")
        
        return [issue.strip() for issue in issues if issue.strip()]