import unittest
from unittest.mock import patch, MagicMock
from agents.reasoning_auditor import ReasoningAuditor
from models.agent_output import AgentOutput
import json

class TestReasoningAuditor(unittest.TestCase):
    def setUp(self):
        self.auditor = ReasoningAuditor("test-api-key")
        self.sample_output_dict = {
            "metadata": {"agent_type": "FileValidationAgent"},
            "valid_rows": 100,
            "invalid_rows": 5,
            "error_messages": ["Row 10: Missing value"]
        }
        self.sample_output = AgentOutput.parse_agent_output(self.sample_output_dict)
        self.expected_structure = ["valid_rows", "invalid_rows", "error_messages"]
    
    @patch('openai.ChatCompletion.create')
    def test_valid_audit(self, mock_openai):
        # Mock OpenAI response for valid output
        mock_response = MagicMock()
        mock_response.choices[0].message.content = "VALID"
        mock_openai.return_value = mock_response
        
        issues = self.auditor.audit_reasoning(self.sample_output, self.expected_structure)
        self.assertEqual(issues, [])
    
    @patch('openai.ChatCompletion.create')
    def test_invalid_audit(self, mock_openai):
        # Mock OpenAI response with issues
        mock_response = MagicMock()
        mock_response.choices[0].message.content = "INCONSISTENCIES:\n- Error count mismatch"
        mock_openai.return_value = mock_response
        
        issues = self.auditor.audit_reasoning(self.sample_output, self.expected_structure)
        self.assertEqual(issues, ["- Error count mismatch"])
    
    @patch('openai.ChatCompletion.create')
    def test_parse_issues(self, mock_openai):
        # Test parsing of different issue formats
        test_cases = [
            ("INCONSISTENCIES:\n- Issue 1\n- Issue 2", ["- Issue 1", "- Issue 2"]),
            ("ISSUES:\n* Problem 1\n* Problem 2", ["* Problem 1", "* Problem 2"]),
            ("Problems found:\n1. First\n2. Second", ["1. First", "2. Second"]),
            ("VALID", []),
            ("", [])
        ]
        
        for response, expected in test_cases:
            mock_response = MagicMock()
            mock_response.choices[0].message.content = response
            mock_openai.return_value = mock_response
            
            issues = self.auditor.audit_reasoning(self.sample_output, self.expected_structure)
            self.assertEqual(issues, expected)
    
    @patch('openai.ChatCompletion.create')
    def test_agent_specific_prompts(self, mock_openai):
        # Test different agent types get appropriate prompts
        agents = [
            ("FileValidationAgent", "schema violations"),
            ("DataValidationAgent", "temporal logic"),
            ("SP_Parser_Agent", "extracted rules")
        ]

        for agent_type, expected_keyword in agents:
            # Create new AgentOutput with different agent type
            modified_dict = self.sample_output_dict.copy()
            modified_dict["metadata"]["agent_type"] = agent_type
            modified_output = AgentOutput.parse_agent_output(modified_dict)

            mock_response = MagicMock()
            mock_response.choices[0].message.content = "ISSUES: Test"
            mock_openai.return_value = mock_response

            self.auditor.audit_reasoning(modified_output, self.expected_structure)
            prompt = mock_openai.call_args[1]["messages"][1]["content"]
            self.assertIn(expected_keyword, prompt)

if __name__ == '__main__':
    unittest.main()