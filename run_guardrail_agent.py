#!/usr/bin/env python3
"""
Production script to run the Guardrail Agent with real agent outputs.

Usage:
    python run_guardrail_agent.py --input agent_output.json
    python run_guardrail_agent.py --input agent_output.json --output audit_report.json
    python run_guardrail_agent.py --stdin  # Read from stdin
"""

import sys
import os
import json
import argparse
from datetime import datetime
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

def setup_argument_parser():
    """Setup command line argument parser."""
    parser = argparse.ArgumentParser(
        description="Run Guardrail Agent audit on real agent outputs",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Audit a single agent output file
    python run_guardrail_agent.py --input data_validation_output.json
    
    # Audit and save report to file
    python run_guardrail_agent.py --input agent_output.json --output audit_report.json
    
    # Read agent output from stdin (for pipeline integration)
    cat agent_output.json | python run_guardrail_agent.py --stdin
    
    # Quiet mode (only output final status)
    python run_guardrail_agent.py --input agent_output.json --quiet
    
    # Verbose mode (show detailed steps)
    python run_guardrail_agent.py --input agent_output.json --verbose
        """
    )
    
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--input', '-i', help='Input JSON file containing agent output')
    input_group.add_argument('--stdin', action='store_true', help='Read agent output from stdin')
    
    parser.add_argument('--output', '-o', help='Output file for audit report (default: stdout)')
    parser.add_argument('--quiet', '-q', action='store_true', help='Quiet mode - minimal output')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose mode - detailed output')
    parser.add_argument('--no-logs', action='store_true', help='Disable file logging (console only)')
    
    return parser

def load_agent_output(args):
    """Load agent output from file or stdin."""
    try:
        if args.stdin:
            if not args.quiet:
                print("Reading agent output from stdin...", file=sys.stderr)
            raw_data = sys.stdin.read()
        else:
            if not args.quiet:
                print(f"Loading agent output from: {args.input}", file=sys.stderr)
            with open(args.input, 'r', encoding='utf-8') as f:
                raw_data = f.read()
        
        agent_output = json.loads(raw_data)
        
        if not args.quiet:
            agent_type = agent_output.get('metadata', {}).get('agent_type', 'UNKNOWN')
            print(f"Loaded output from: {agent_type}", file=sys.stderr)
        
        return agent_output
        
    except FileNotFoundError:
        print(f"Error: Input file '{args.input}' not found", file=sys.stderr)
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in input - {e}", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error loading agent output: {e}", file=sys.stderr)
        sys.exit(1)

def initialize_guardrail_agent(args):
    """Initialize the Guardrail Agent."""
    try:
        from agents.guardrail_agent import GuardrailAgent
        
        if args.no_logs:
            # Initialize with custom logger that doesn't write to files
            from core.telemetry_logger import TelemetryLogger
            logger = TelemetryLogger(log_file=None, human_readable_log=None)
            agent = GuardrailAgent()
            agent.logger = logger
        else:
            agent = GuardrailAgent()
        
        if not args.quiet:
            print("✓ Guardrail Agent initialized successfully", file=sys.stderr)
        
        return agent
        
    except ValueError as e:
        print(f"Error: {e}", file=sys.stderr)
        print("Please ensure your OPENAI_API_KEY is set in the .env file or environment", file=sys.stderr)
        sys.exit(1)
    except Exception as e:
        print(f"Error initializing Guardrail Agent: {e}", file=sys.stderr)
        sys.exit(1)

def run_audit(agent, agent_output, args):
    """Run the audit and return the report."""
    try:
        if args.verbose:
            print("Starting audit process...", file=sys.stderr)
        
        start_time = datetime.utcnow()
        audit_report = agent.audit_agent_output(agent_output)
        end_time = datetime.utcnow()
        
        # Add timing information
        processing_time = (end_time - start_time).total_seconds() * 1000
        audit_report['processing_time_ms'] = round(processing_time, 2)
        audit_report['audit_timestamp'] = end_time.isoformat()
        
        if args.verbose:
            print(f"Audit completed in {processing_time:.2f}ms", file=sys.stderr)
        
        return audit_report
        
    except Exception as e:
        print(f"Error during audit: {e}", file=sys.stderr)
        sys.exit(1)

def display_results(audit_report, args):
    """Display audit results based on verbosity level."""
    if args.quiet:
        # Quiet mode: only output status
        print(audit_report['status'])
        return
    
    status = audit_report.get('status', 'UNKNOWN')
    agent_type = audit_report.get('agent_type', 'UNKNOWN')
    processing_time = audit_report.get('processing_time_ms', 0)
    
    print(f"\n=== GUARDRAIL AUDIT REPORT ===", file=sys.stderr)
    print(f"Agent Type: {agent_type}", file=sys.stderr)
    print(f"Status: {status}", file=sys.stderr)
    print(f"Processing Time: {processing_time:.2f}ms", file=sys.stderr)
    
    if status != 'PASSED':
        findings = audit_report.get('findings', {})
        print(f"\nFindings:", file=sys.stderr)
        
        for category, issues in findings.items():
            if issues:
                print(f"  {category.upper().replace('_', ' ')}:", file=sys.stderr)
                for issue in issues:
                    print(f"    - {issue}", file=sys.stderr)
    
    if 'escalation' in audit_report:
        escalation = audit_report['escalation']
        print(f"\n🚨 ESCALATION REQUIRED:", file=sys.stderr)
        print(f"  Type: {escalation.get('type', 'UNKNOWN')}", file=sys.stderr)
        print(f"  Severity: {escalation.get('severity', 'UNKNOWN')}", file=sys.stderr)
        print(f"  Action: {escalation.get('action_required', 'Review required')}", file=sys.stderr)
    
    if args.verbose and 'validation_errors' in audit_report:
        print(f"\nValidation Errors:", file=sys.stderr)
        for error in audit_report['validation_errors']:
            print(f"  - {error}", file=sys.stderr)
    
    print("=" * 30, file=sys.stderr)

def save_report(audit_report, args):
    """Save audit report to file or stdout."""
    try:
        report_json = json.dumps(audit_report, indent=2, ensure_ascii=False)
        
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                f.write(report_json)
            if not args.quiet:
                print(f"Audit report saved to: {args.output}", file=sys.stderr)
        else:
            # Output to stdout
            print(report_json)
            
    except Exception as e:
        print(f"Error saving report: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    """Main function."""
    parser = setup_argument_parser()
    args = parser.parse_args()
    
    # Load agent output
    agent_output = load_agent_output(args)
    
    # Initialize Guardrail Agent
    agent = initialize_guardrail_agent(args)
    
    # Run audit
    audit_report = run_audit(agent, agent_output, args)
    
    # Display results (to stderr for visibility)
    display_results(audit_report, args)
    
    # Save report (to file or stdout)
    save_report(audit_report, args)
    
    # Exit with appropriate code
    status = audit_report.get('status', 'ERROR')
    if status == 'PASSED':
        sys.exit(0)
    elif status in ['WARNING', 'VALIDATION_ERROR']:
        sys.exit(1)
    elif status in ['CRITICAL', 'ERROR']:
        sys.exit(2)
    else:
        sys.exit(3)

if __name__ == "__main__":
    main()
