import json
from models.agent_output import AgentOutput

class ProcessCompliance:
    def verify_process(self, agent_output: AgentOutput, profile):
        """Verify agent followed its defined process"""
        issues = []

        # Note: Input validation is now handled by Pydantic model
        # metadata is guaranteed to exist and be properly structured

        # 1. Check processing time
        max_time = profile.get('max_processing_time_ms', 5000)
        if agent_output.metadata.processing_time_ms is not None:
            if agent_output.metadata.processing_time_ms > max_time:
                issues.append(f"Processing time exceeded ({agent_output.metadata.processing_time_ms}ms > {max_time}ms)")
        else:
            issues.append("Missing processing_time_ms in metadata")

        # 2. Verify output structure
        if 'expected_output_structure' in profile:
            expected_keys = set(profile['expected_output_structure'])
            # Get actual keys from the model, excluding None values
            actual_dict = agent_output.model_dump_dict()
            actual_keys = set(actual_dict.keys())
            missing_keys = expected_keys - actual_keys

            if missing_keys:
                issues.append(f"Missing output elements: {', '.join(missing_keys)}")

            # Only flag extra keys as warnings, not errors (more flexible)
            extra_keys = actual_keys - expected_keys
            if extra_keys and len(extra_keys) > 2:  # Allow some flexibility
                issues.append(f"Many unexpected output elements: {', '.join(list(extra_keys)[:3])}...")

        # 3. Agent-specific process checks
        agent_type = agent_output.metadata.agent_type
        if agent_type == "FileValidationAgent":
            if agent_output.validation_coverage is not None:
                if agent_output.validation_coverage < profile['min_coverage']:
                    issues.append(f"Validation coverage insufficient: {agent_output.validation_coverage*100}% < {profile['min_coverage']*100}%")

        elif agent_type == "DataValidationAgent":
            if agent_output.override_rate is not None:
                if agent_output.override_rate > profile['max_override_rate']:
                    issues.append(f"High override rate: {agent_output.override_rate*100}% > {profile['max_override_rate']*100}%")

        elif agent_type == "SP_Parser_Agent":
            if agent_output.parsing_confidence is not None and 'min_rules_coverage' in profile:
                if agent_output.parsing_confidence < profile['min_rules_coverage']:
                    issues.append(f"Parsing confidence insufficient: {agent_output.parsing_confidence*100}% < {profile['min_rules_coverage']*100}%")

        return issues