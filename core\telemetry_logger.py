import json
import logging
from datetime import datetime

class TelemetryLogger:
    def __init__(self, log_file="audit_logs.jsonl", human_readable_log="audit_human_readable.log"):
        self.log_file = log_file
        self.human_readable_log = human_readable_log
        self._setup_console_logger()

    def _setup_console_logger(self):
        """Setup a human-readable console logger"""
        self.console_logger = logging.getLogger("GuardrailLogger")
        self.console_logger.setLevel(logging.INFO)

        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - GuardrailAgent - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)

        if not self.console_logger.handlers:
            self.console_logger.addHandler(handler)

    def log_audit_start(self, metadata):
        agent_type = metadata.get("agent_type", "UNKNOWN")
        task_id = metadata.get("task_id", "UNKNOWN")

        self.console_logger.info(f"[START] Auditing output from {agent_type}")
        self._write_human_readable(f"[START] Audit initiated for {agent_type} (Task: {task_id})")

        self._write_log({
            "timestamp": datetime.utcnow().isoformat(),
            "event": "AUDIT_START",
            "agent_type": agent_type,
            "agent_version": metadata.get("agent_version", "UNKNOWN"),
            "task_id": task_id
        })

    def log_audit_complete(self, report):
        findings = report.get("findings", {})
        status = report.get("status", "UNKNOWN")
        agent_type = report.get("agent_type", "UNKNOWN")

        self.console_logger.info(f"[DONE] Audit completed with status: {status}")

        # Human-readable logging
        if status == "PASSED":
            self._write_human_readable(f"[OK] {agent_type} audit completed successfully - No issues found")
        elif status == "WARNING":
            process_count = len(findings.get("process_issues", []))
            self._write_human_readable(f"[WARN] {agent_type} audit completed with {process_count} process issues")
        elif status == "CRITICAL":
            security_count = len(findings.get("security_issues", []))
            reasoning_count = len(findings.get("reasoning_issues", []))
            if security_count > 0:
                self._write_human_readable(f"[CRITICAL] {agent_type} audit failed - {security_count} security issues detected")
            elif reasoning_count > 0:
                self._write_human_readable(f"[CRITICAL] {agent_type} audit failed - {reasoning_count} reasoning issues detected")
        elif status == "VALIDATION_ERROR":
            self._write_human_readable(f"[ERROR] {agent_type} audit failed - Invalid output structure")
        else:
            self._write_human_readable(f"[ERROR] {agent_type} audit failed - {status}")

        # Console warnings for detailed issues
        if findings.get("process_issues"):
            self.console_logger.warning(f"[WARN] Process issues: {len(findings['process_issues'])}")
        if findings.get("reasoning_issues"):
            self.console_logger.warning(f"[WARN] Reasoning issues: {len(findings['reasoning_issues'])}")
        if findings.get("security_issues"):
            self.console_logger.warning(f"[WARN] Security issues: {len(findings['security_issues'])}")

        # Structured JSON logging (unchanged)
        self._write_log({
            "timestamp": datetime.utcnow().isoformat(),
            "event": "AUDIT_COMPLETE",
            "agent_type": agent_type,
            "status": status,
            "processing_time_ms": report.get("processing_time_ms", 0),
            "findings_count": {
                "process_issues": len(findings.get("process_issues", [])),
                "reasoning_issues": len(findings.get("reasoning_issues", [])),
                "security_issues": len(findings.get("security_issues", []))
            }
        })

    def log_escalation(self, escalation):
        reason = escalation.get("reason", "unknown reason")
        severity = escalation.get("severity", "MEDIUM")
        escalation_type = escalation.get("type", "UNKNOWN")

        self.console_logger.info(f"[ESCALATE] Human review triggered due to: {reason}")
        self._write_human_readable(f"[ESCALATE] {severity} escalation - {escalation_type} issue requires human review")

        self._write_log({
            "timestamp": datetime.utcnow().isoformat(),
            "event": "ESCALATION",
            "reason": reason,
            "severity": severity,
            "task_id": escalation.get("task_id", "UNKNOWN")
        })

    def _write_log(self, log_entry):
        """Append structured JSON log entry to file"""
        try:
            with open(self.log_file, "a", encoding='utf-8') as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            self.console_logger.warning(f"[ERROR] Failed to write log to {self.log_file}: {e}")

    def _write_human_readable(self, message):
        """Append human-readable log entry to file"""
        try:
            timestamp = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC")
            with open(self.human_readable_log, "a", encoding='utf-8') as f:
                f.write(f"{timestamp} - {message}\n")
        except Exception as e:
            self.console_logger.warning(f"[ERROR] Failed to write human-readable log to {self.human_readable_log}: {e}")

    def log_step(self, step_name, agent_type=None):
        """Log individual audit steps for detailed tracking"""
        if agent_type:
            message = f"[STEP] {step_name} for {agent_type}"
        else:
            message = f"[STEP] {step_name}"

        self.console_logger.debug(message)
        self._write_human_readable(message)

    def log_validation_error(self, agent_type, error_details):
        """Log validation errors specifically"""
        message = f"[VALIDATION_ERROR] {agent_type} output validation failed - {error_details}"
        self.console_logger.error(message)
        self._write_human_readable(message)

    def log_error(self, message):
        """Log general errors"""
        formatted_message = f"[ERROR] {message}"
        self.console_logger.error(formatted_message)
        self._write_human_readable(formatted_message)

    def log_warning(self, message):
        """Log warnings"""
        formatted_message = f"[WARN] {message}"
        self.console_logger.warning(formatted_message)
        self._write_human_readable(formatted_message)

    def log_ok(self, message):
        """Log success messages"""
        formatted_message = f"[OK] {message}"
        self.console_logger.info(formatted_message)
        self._write_human_readable(formatted_message)

    def log_done(self, message):
        """Log completion messages"""
        formatted_message = f"[DONE] {message}"
        self.console_logger.info(formatted_message)
        self._write_human_readable(formatted_message)
