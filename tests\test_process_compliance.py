import unittest
from agents.process_compliance import ProcessCompliance
from models.agent_output import AgentOutput

class TestProcessCompliance(unittest.TestCase):
    def setUp(self):
        self.compliance = ProcessCompliance()
        self.sample_output_dict = {
            "metadata": {
                "agent_type": "FileValidationAgent",
                "processing_time_ms": 2500,
                "agent_version": "1.2.3"
            },
            "validation_coverage": 0.99
        }
        self.sample_output = AgentOutput.parse_agent_output(self.sample_output_dict)
        self.profile = {
            "max_processing_time_ms": 3000,
            "min_coverage": 0.98,
            "expected_output_structure": ["metadata", "validation_coverage"]
        }
    
    def test_clean_compliance(self):
        issues = self.compliance.verify_process(self.sample_output, self.profile)
        self.assertEqual(issues, [])
    
    def test_processing_time_exceeded(self):
        # Create new AgentOutput with exceeded processing time
        modified_dict = self.sample_output_dict.copy()
        modified_dict["metadata"]["processing_time_ms"] = 3500
        modified_output = AgentOutput.parse_agent_output(modified_dict)
        issues = self.compliance.verify_process(modified_output, self.profile)
        self.assertEqual(issues, ["Processing time exceeded (3500ms > 3000ms)"])

    def test_coverage_insufficient(self):
        # Create new AgentOutput with insufficient coverage
        modified_dict = self.sample_output_dict.copy()
        modified_dict["validation_coverage"] = 0.97
        modified_output = AgentOutput.parse_agent_output(modified_dict)
        issues = self.compliance.verify_process(modified_output, self.profile)
        self.assertEqual(issues, ["Validation coverage insufficient: 97.0% < 98.0%"])

    def test_missing_output_field(self):
        # Create new AgentOutput without validation_coverage
        modified_dict = self.sample_output_dict.copy()
        del modified_dict["validation_coverage"]
        modified_output = AgentOutput.parse_agent_output(modified_dict)
        issues = self.compliance.verify_process(modified_output, self.profile)
        self.assertEqual(issues, ["Missing output elements: validation_coverage"])

    def test_extra_output_field(self):
        # Note: Pydantic model prevents extra fields, so this test is no longer relevant
        # The model validation would fail before reaching process compliance
        pass

    def test_data_validation_specific(self):
        # Test DataValidationAgent specific checks
        modified_dict = {
            "metadata": {
                "agent_type": "DataValidationAgent",
                "processing_time_ms": 2500,
                "agent_version": "1.2.3"
            },
            "override_rate": 0.07
        }
        modified_output = AgentOutput.parse_agent_output(modified_dict)
        profile = self.profile.copy()
        profile["max_override_rate"] = 0.05

        issues = self.compliance.verify_process(modified_output, profile)
        self.assertEqual(issues, ["High override rate: 7.0% > 5.0%"])

    def test_sp_parser_specific(self):
        # Test SP_Parser_Agent specific checks
        modified_dict = {
            "metadata": {
                "agent_type": "SP_Parser_Agent",
                "processing_time_ms": 2500,
                "agent_version": "1.2.3"
            },
            "parsing_confidence": 0.92
        }
        modified_output = AgentOutput.parse_agent_output(modified_dict)
        profile = self.profile.copy()
        profile["min_rules_coverage"] = 0.95

        issues = self.compliance.verify_process(modified_output, profile)
        self.assertEqual(issues, ["Parsing confidence insufficient: 92.0% < 95.0%"])

if __name__ == '__main__':
    unittest.main()