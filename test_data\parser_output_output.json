{"metadata": {"agent_type": "SP_Parser_Agent", "agent_version": "1.5.3", "processing_time_ms": 6800, "task_id": "sp-parse-2025-01", "timestamp": "2025-07-13T12:00:00Z"}, "rules_json": {"validation_rules": [{"rule_id": "NAV-01", "field": "nav_value", "constraint": "NOT NULL AND > 0", "error_message": "NAV value must be positive"}, {"rule_id": "FUND-ID-02", "field": "fund_id", "constraint": "LENGTH = 10 AND PATTERN = 'FUND[0-9]{6}'", "error_message": "Fund ID must follow FUND###### format"}]}, "column_template": {"fund_id": "VARCHAR(10)", "nav_value": "DECIMAL(15,4)", "effective_date": "DATE"}, "parsing_confidence": 0.97}